# PHP Endpoints


<!-- Plugin description -->
A powerful PhpStorm plugin that scans and displays all API endpoints in your PHP projects.
<!---Plugin description end -->

**Features:**
- 🔍 **Automatic Detection**: Intelligently scans Laravel PHP projects
- 🎯 **Quick Navigation**: Double-click any endpoint to jump directly to its definition in your code
- ⚡ **Real-time Scanning**: Fast scanning with intelligent caching for better performance
- 🎨 **Color-coded Methods**: HTTP methods are color-coded for easy identification (GET, POST, PUT, DELETE, etc.)
- ⚡ **Event listeners and publishers**: BiNavigate to event listeners from event declarations
- ⚡ **Eloquent events**: Navigate to controller actions from Eloquent model events
- 🧉 **Snapshot support**: Navigate to snapshot files from test methods

**Supported Frameworks:**
- <PERSON><PERSON> (Routes and Controllers)

This plugin helps developers quickly understand and navigate their API structure, making it easier to work with large PHP codebases.

<!-- Plugin description end -->

## Installation
- Goto the [latest artifacts](https://gitlab.rightcapital.io/developer-tools/php-endpoints/-/artifacts) page, download **plugins.zip** file 
- Unzip it, you will found a file start with `php-endpoints-xxx.zip` at the **distributions** directory 
- Install it manually using
  <kbd>Settings/Preferences</kbd> > <kbd>Plugins</kbd> > <kbd>⚙️</kbd> > <kbd>Install plugin from disk...</kbd> select the `php-endpoints-xxx.zip` file
- Restart IDE

## Configuration

The plugin automatically uses PhpStorm's configured PHP interpreter, but you can override it if needed:

1. Go to <kbd>Settings/Preferences</kbd> > <kbd>Tools</kbd> > <kbd>PHP Endpoints</kbd>
2. The configuration page shows:

### PhpStorm Project Configuration
- **Automatic Detection**: The plugin automatically uses your project's configured PHP interpreter from <kbd>Settings</kbd> > <kbd>PHP</kbd>
- **Priority Order**: 
  1. PhpStorm project's CLI interpreter (highest priority)
  2. Override path (if specified)
  3. System detection (`/opt/homebrew/bin/php`, `/usr/local/bin/php`, `/usr/bin/php`)

### Override PHP Executable Path (Optional)
- **Leave Empty**: To use PhpStorm's automatic detection (recommended)
- **Custom Path**: Click the browse button to override with a specific PHP executable
- **Version Display**: Shows the effective PHP path and version being used

### macOS Users
If you're using Homebrew, the plugin will automatically detect:
- **Apple Silicon Macs**: `/opt/homebrew/bin/php`
- **Intel Macs**: `/usr/local/bin/php`

### Troubleshooting
- **Routes not detected**: 
  1. Check your project's PHP interpreter in <kbd>Settings</kbd> > <kbd>PHP</kbd>
  2. Verify `php artisan route:list --json` works in your terminal
  3. Override PHP path in plugin settings if needed
- **Wrong PHP version**: Configure the correct interpreter in <kbd>Settings</kbd> > <kbd>PHP</kbd> > <kbd>CLI Interpreter</kbd>
- **Path issues**: The plugin will show the effective PHP path in the settings page
- **API compatibility**: The plugin automatically detects PhpStorm API changes and falls back to system detection
- **Debug info**: Check IDE logs for detailed scanning information and PhpStorm API status
