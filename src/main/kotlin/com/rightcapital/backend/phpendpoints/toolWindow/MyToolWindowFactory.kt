package com.rightcapital.backend.phpendpoints.toolWindow

import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.ui.content.ContentFactory

class MyToolWindowFactory : ToolWindowFactory {

    private val logger = thisLogger()

    override fun createToolWindowContent(project: Project, toolWindow: ToolWindow) {
        logger.info("Creating PHP Endpoints tool window for project: ${project.name}")

        val endpointListPanel = EndpointListPanel(project)
        val content = ContentFactory.getInstance().createContent(
            endpointListPanel,
            "PHP Endpoints",
            false
        )
        toolWindow.contentManager.addContent(content)
    }

    override fun shouldBeAvailable(project: Project): Boolean {
        // Only show the tool window if the project contains PHP files
        return project.baseDir?.let { baseDir ->
            hasPhpFiles(baseDir)
        } ?: false
    }

    private fun hasPhpFiles(dir: VirtualFile): Boolean {
        return try {
            dir.children.any { child ->
                when {
                    child.isDirectory -> hasPhpFiles(child)
                    child.extension?.lowercase() == "php" -> true
                    else -> false
                }
            }
        } catch (e: Exception) {
            logger.warn("Error checking for PHP files: ${e.message}")
            true // Show tool window by default if we can't determine
        }
    }
}
