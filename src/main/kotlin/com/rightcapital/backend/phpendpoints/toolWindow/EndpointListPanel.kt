package com.rightcapital.backend.phpendpoints.toolWindow

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.fileTypes.FileTypeManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiManager
import com.intellij.psi.search.FileTypeIndex
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.psi.util.PsiTreeUtil
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPanel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTextField
import com.intellij.ui.table.JBTable
import com.intellij.util.ui.JBUI
import com.jetbrains.php.lang.psi.elements.Method
import com.jetbrains.php.lang.psi.elements.PhpClass
import com.rightcapital.backend.phpendpoints.models.ApiEndpoint
import com.rightcapital.backend.phpendpoints.models.PhpFramework
import com.rightcapital.backend.phpendpoints.services.PhpEndpointScanner
import java.awt.*
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import javax.swing.*
import javax.swing.event.DocumentEvent
import javax.swing.event.DocumentListener

class EndpointListPanel(private val project: Project) : JBPanel<EndpointListPanel>() {

    private val scanner = project.service<PhpEndpointScanner>()
    private val tableModel = EndpointTableModel()
    private val table = JBTable(tableModel)
    private val refreshButton = JButton("Refresh")
    private val statusLabel = JBLabel("Ready")
    private val frameworkLabel = JBLabel("")
    private val searchField = JBTextField()
    private val clearSearchButton = JButton("Clear")

    private var allEndpoints = listOf<ApiEndpoint>()

    init {
        setupUI()
        setupEventHandlers()
        refreshEndpoints()
    }

    private fun setupUI() {
        layout = BorderLayout()

        val controlPanel = setupPanelUI()
        setupTable()

        // Main content
        val scrollPane = JBScrollPane(table).apply {
            preferredSize = Dimension(800, 400)
        }

        add(controlPanel, BorderLayout.NORTH)
        add(scrollPane, BorderLayout.CENTER)
    }

    private fun setupPanelUI(
    ): JBPanel<JBPanel<*>> {
        // Search panel
        val searchPanel = setupSearchPanelUI()
        // Top panel with controls
        val topPanel = setupControllerUI()

        return JBPanel<JBPanel<*>>().apply {
            layout = BorderLayout()
            add(searchPanel, BorderLayout.NORTH)
            add(topPanel, BorderLayout.SOUTH)
        }
    }

    private fun setupControllerUI(): JBPanel<JBPanel<*>> = JBPanel<JBPanel<*>>().apply {
        layout = BoxLayout(this, BoxLayout.X_AXIS)
        border = JBUI.Borders.empty(5)

        add(refreshButton)
        add(Box.createHorizontalStrut(10))
        add(frameworkLabel)
        add(Box.createHorizontalGlue())
        add(statusLabel)
    }

    private fun setupSearchPanelUI(): JBPanel<JBPanel<*>> = JBPanel<JBPanel<*>>().apply {
        layout = BoxLayout(this, BoxLayout.X_AXIS)
        border = JBUI.Borders.empty(5, 5, 0, 5)

        add(JBLabel("Search URL: "))
        add(Box.createHorizontalStrut(5))
        add(searchField.apply {
            preferredSize = Dimension(400, preferredSize.height)
            maximumSize = Dimension(500, preferredSize.height)
            toolTipText = "Enter URL to search (supports fuzzy matching)"
        })
        add(Box.createHorizontalStrut(5))
        add(clearSearchButton.apply {
            toolTipText = "Clear search"
        })
        add(Box.createHorizontalGlue())
    }

    private fun setupTable() {
        table.apply {
            fillsViewportHeight = true
            setShowGrid(true)

            // Set column widths
            columnModel.getColumn(0).preferredWidth = 80  // Method
            columnModel.getColumn(1).preferredWidth = 400 // URL (increased width)
            columnModel.getColumn(2).preferredWidth = 250 // Controller (increased width)
            columnModel.getColumn(3).preferredWidth = 200 // Method Name (increased width)

            // Custom renderer for HTTP methods
            columnModel.getColumn(0).cellRenderer = HttpMethodRenderer()

            // Custom renderer for Controller column to show it's clickable
            columnModel.getColumn(2).cellRenderer = ControllerRenderer()

            // Custom renderer for Method Name column to show it's clickable
            columnModel.getColumn(3).cellRenderer = MethodNameRenderer()
        }
    }

    private fun setupEventHandlers() {
        refreshButton.addActionListener {
            refreshEndpoints()
        }

        // Search functionality
        searchField.document.addDocumentListener(object : DocumentListener {
            override fun insertUpdate(e: DocumentEvent?) = performSearch()
            override fun removeUpdate(e: DocumentEvent?) = performSearch()
            override fun changedUpdate(e: DocumentEvent?) = performSearch()
        })

        clearSearchButton.addActionListener {
            searchField.text = ""
            performSearch()
        }

        // Handle table clicks
        table.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                val selectedRow = table.selectedRow
                val selectedColumn = table.selectedColumn

                if (selectedRow >= 0) {
                    val endpoint = tableModel.getEndpointAt(selectedRow)
                    val clickControllerCell = e.clickCount == 1 && selectedColumn == 2
                    val clickMethodCell = e.clickCount == 1 && selectedColumn == 3
                    val doubleClick = e.clickCount == 2
                    when {
                        clickControllerCell -> {
                            handleClickControllerCell(endpoint)
                        }

                        clickMethodCell -> {
                            handleClickMethodCell(endpoint)
                        }

                        doubleClick -> {
                            handleClickMethodCell(endpoint)
                        }
                    }
                }
            }

            private fun handleClickMethodCell(endpoint: ApiEndpoint) {
                if (!endpoint.controllerClass.isNullOrBlank() && !endpoint.methodName.isNullOrBlank()) {
                    openControllerMethod(
                        endpoint.controllerClass,
                        endpoint.methodName,
                        endpoint.fullControllerPath
                    )
                }
            }

            private fun handleClickControllerCell(endpoint: ApiEndpoint) {
                if (!endpoint.controllerClass.isNullOrBlank()) {
                    openControllerFile(
                        endpoint.controllerClass,
                        endpoint.methodName,
                        endpoint.fullControllerPath
                    )
                }
            }
        })
    }

    private fun refreshEndpoints() {
        SwingUtilities.invokeLater {
            statusLabel.text = "Scanning..."
            refreshButton.isEnabled = false
        }

        // Run scan in background thread with proper read access
        Thread {
            try {
                val framework = ReadAction.compute<PhpFramework, Exception> {
                    scanner.detectFramework()
                }
                val endpoints = ReadAction.compute<List<ApiEndpoint>, Exception> {
                    scanner.scanAllEndpoints()
                }

                SwingUtilities.invokeLater {
                    allEndpoints = endpoints
                    performSearch() // Apply current search filter
                    frameworkLabel.text = "Framework: ${framework.displayName}"
                    updateStatusLabel()
                    refreshButton.isEnabled = true
                }
            } catch (e: Exception) {
                SwingUtilities.invokeLater {
                    statusLabel.text = "Error: ${e.message}"
                    refreshButton.isEnabled = true
                }
            }
        }.start()
    }

    private fun performSearch() {
        val searchText = searchField.text.trim()
        val filteredEndpoints = if (searchText.isEmpty()) {
            allEndpoints
        } else {
            allEndpoints.filter { endpoint ->
                fuzzyMatch(endpoint.url.lowercase(), searchText.lowercase())
            }
        }

        tableModel.setEndpoints(filteredEndpoints)
        updateStatusLabel()
    }

    private fun fuzzyMatch(text: String, pattern: String): Boolean {
        if (pattern.isEmpty()) return true
        if (text.isEmpty()) return false

        // Simple fuzzy matching: check if all characters in pattern appear in order in text
        var patternIndex = 0
        for (char in text) {
            if (patternIndex < pattern.length && char == pattern[patternIndex]) {
                patternIndex++
            }
        }

        // Also check for substring match for better user experience
        return patternIndex == pattern.length || text.contains(pattern)
    }

    private fun updateStatusLabel() {
        val searchText = searchField.text.trim()
        val displayedCount = tableModel.rowCount
        val totalCount = allEndpoints.size

        statusLabel.text = if (searchText.isEmpty()) {
            "Found $totalCount endpoints"
        } else {
            "Showing $displayedCount of $totalCount endpoints"
        }
    }

    private fun openControllerMethod(controllerClass: String, methodName: String?, fullControllerPath: String?) {
        if (methodName.isNullOrBlank()) {
            openControllerFile(controllerClass, methodName, fullControllerPath)
            return
        }

        ReadAction.run<Exception> {
            try {
                val foundFile: VirtualFile? = findVirtualFile(controllerClass,fullControllerPath)

                SwingUtilities.invokeLater {
                    if (foundFile != null) {
                        // Open the file first
                        val fileEditorManager = FileEditorManager.getInstance(project)
                        val editors = fileEditorManager.openFile(foundFile, true)

                        if (editors.isNotEmpty()) {
                            // Get the current editor
                            val currentEditor = editors.firstOrNull { it is TextEditor } as? TextEditor
                            if (currentEditor != null) {
                                gotoMethod(foundFile, methodName, currentEditor)
                            }
                            statusLabel.text = "Opened $controllerClass with structure popup"
                        } else {
                            statusLabel.text = "Failed to open $controllerClass"
                        }
                    } else {
                        statusLabel.text = "Controller file not found: $controllerClass.php"
                    }
                }
            } catch (e: Exception) {
                SwingUtilities.invokeLater {
                    statusLabel.text = "Error opening controller: ${e.message}"
                }
            }
        }
    }

    private fun gotoMethod(virtualFile: VirtualFile, methodName: String, editor: TextEditor) {
        ReadAction.run<Exception> {
            try {
                val psiManager = PsiManager.getInstance(project)
                val psiFile = psiManager.findFile(virtualFile)

                if (psiFile == null) {
                    SwingUtilities.invokeLater {
                        statusLabel.text = "Could not load PSI file"
                    }
                    return@run
                }
                val methodLocation = findMethodInPsiFile(psiFile, methodName)
                if (methodLocation != null) {
                    SwingUtilities.invokeLater {
                        val fileEditorManager = FileEditorManager.getInstance(project)
                        val editors = fileEditorManager.openFile(methodLocation.first, true)
                        if (editors.isNotEmpty()) {
                            val textEditor = editors.firstOrNull { it is TextEditor } as? TextEditor
                            if (textEditor != null) {
                                navigateToLineInEditor(textEditor, methodLocation.second)
                            }
                        }

                        statusLabel.text =
                            "Found method $methodName in ${methodLocation.first.name} at line ${methodLocation.second}"
                    }
                    return@run
                }
                val projectMethodLocation = searchMethodInProject(methodName)
                if (projectMethodLocation == null) {
                    statusLabel.text = "Opened controller file (method $methodName not found)"
                    return@run
                }
                SwingUtilities.invokeLater {
                    val fileEditorManager = FileEditorManager.getInstance(project)
                    val editors = fileEditorManager.openFile(projectMethodLocation.first, true)
                    if (editors.isNotEmpty()) {
                        val textEditor = editors.firstOrNull { it is TextEditor } as? TextEditor
                        if (textEditor != null) {
                            navigateToLineInEditor(textEditor, projectMethodLocation.second)
                        }
                    }
                    statusLabel.text =
                        "Found method $methodName in ${projectMethodLocation.first.name} at line ${projectMethodLocation.second}"
                }

            } catch (e: Exception) {
                SwingUtilities.invokeLater {
                    statusLabel.text = "Error in showFileStructurePopup: ${e.message}"
                }
            }
        }
    }

    private fun navigateToLineInEditor(editor: TextEditor, lineNumber: Int) {
        try {
            ReadAction.run<RuntimeException> {
                val document = editor.editor.document
                val line = (lineNumber - 1).coerceIn(0, document.lineCount - 1)
                val offset = document.getLineStartOffset(line)

                // Move cursor to the method location
                editor.editor.caretModel.moveToOffset(offset)

                // Scroll to make the line visible
                editor.editor.scrollingModel.scrollToCaret(ScrollType.CENTER)

                // Select the line to highlight it
                val lineEndOffset = document.getLineEndOffset(line)
                editor.editor.selectionModel.setSelection(offset, lineEndOffset)
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun openControllerFile(controllerClass: String, methodName: String?, fullControllerPath: String?) {
        ReadAction.run<Exception> {
            try {
                var foundFile: VirtualFile? = findVirtualFile(controllerClass, fullControllerPath)

                SwingUtilities.invokeLater {
                    if (foundFile != null) {
                        val fileEditorManager = FileEditorManager.getInstance(project)
                        val editors = fileEditorManager.openFile(foundFile, true)

                        if (editors.isNotEmpty()) {
                            statusLabel.text = "Opened $controllerClass" +
                                    if (!methodName.isNullOrBlank()) " (method: $methodName)" else ""
                        } else {
                            statusLabel.text = "Failed to open $controllerClass"
                        }
                    } else {
                        statusLabel.text = "Controller file not found: $controllerClass.php"
                    }
                }
            } catch (e: Exception) {
                SwingUtilities.invokeLater {
                    statusLabel.text = "Error opening controller: ${e.message}"
                }
            }
        }
    }

    private fun findVirtualFile(controllerClass: String, fullControllerPath: String?): VirtualFile? {
        // Generate possible paths for the controller file
        val possiblePaths = generateControllerPaths(controllerClass, fullControllerPath)
        var foundFile: VirtualFile? = null

        // Try to find the file using the possible paths
        for (path in possiblePaths) {
            val fullPath = project.basePath + "/" + path
            val virtualFile = LocalFileSystem.getInstance().findFileByPath(fullPath)
            if (virtualFile != null && virtualFile.exists()) {
                foundFile = virtualFile
                break
            }
        }
        return foundFile
    }

    private fun findMethodInPsiFile(psiFile: PsiFile, methodName: String): Pair<VirtualFile, Int>? {
        val psiMethod = findMethodUsingPhpPsi(psiFile, methodName)
        if (psiMethod?.second != null) {
            val offset = psiMethod.second!!.textOffset
            val targetPhpClass = psiMethod.first
            val targetPsiFile = targetPhpClass.containingFile

            val document = PsiDocumentManager.getInstance(project).getDocument(targetPsiFile)
            if (document != null) {
                val lineNumber = document.getLineNumber(offset) + 1
                return targetPsiFile.virtualFile to lineNumber
            }
        }

        return null
    }

    private fun searchMethodInProject(methodName: String): Pair<VirtualFile, Int>? {
        try {

            // Search for PHP files that might contain the method (including vendor directories)
            val projectScope = GlobalSearchScope.allScope(project)
            val fileTypeManager = FileTypeManager.getInstance()
            val phpFileType = fileTypeManager.getFileTypeByExtension("php")
            val phpFiles = FileTypeIndex.getFiles(phpFileType, projectScope)

            var checkedFiles = 0
            for (virtualFile in phpFiles) {
                // Skip if it's not likely a trait file
                if (!virtualFile.name.endsWith(".php")) {
                    continue
                }

                checkedFiles++

                val psiManager = PsiManager.getInstance(project)
                val psiFile = psiManager.findFile(virtualFile)

                if (psiFile != null) {
                    val methodLocation = findMethodInPsiFile(psiFile, methodName)
                    if (methodLocation != null) {
                        return methodLocation
                    }
                }

                // Limit the search to avoid too much output
                if (checkedFiles > 50) {
                    break
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }

        return null
    }


    private fun generateControllerPaths(controllerClass: String, fullControllerPath: String?): List<String> {
        val paths = mutableListOf<String>()

        // If we have the full controller path, use it to construct the exact file path
        if (!fullControllerPath.isNullOrBlank()) {
            val namespacePath = fullControllerPath
                .replace("\\", "/")
                .replace("App/Http/Controllers", "app/Http/Controllers")
            paths.add("$namespacePath.php")
            
            // Handle vendor paths for third-party packages
            if (!fullControllerPath.startsWith("App\\")) {
                val vendorPath = fullControllerPath
                    .replace("\\", "/")
                    .lowercase()
                
                // Try common vendor path patterns
                paths.add("vendor/$vendorPath.php")
                
                // Split namespace and try to construct vendor path
                val parts = fullControllerPath.split("\\")
                if (parts.size >= 2) {
                    val vendor = parts[0].lowercase()
                    val packageParts = parts.drop(1)
                    
                    // For vendor packages, usually the first part after vendor is the package name
                    if (packageParts.isNotEmpty()) {
                        val packageName = packageParts[0].replace(Regex("([a-z])([A-Z])"), "$1-$2")
                            .replace(Regex("([A-Z]+)([A-Z][a-z])"), "$1-$2")
                            .lowercase()
                        
                        // Rest of the path after package name
                        val restParts = packageParts.drop(1)
                        val restPath = restParts.mapIndexed { index, part ->
                            if (index == restParts.size - 1) {
                                // Keep the last part (class name) as-is
                                part
                            } else {
                                // Convert PascalCase/camelCase to kebab-case for directory names
                                part.replace(Regex("([a-z])([A-Z])"), "$1-$2")
                                    .replace(Regex("([A-Z]+)([A-Z][a-z])"), "$1-$2")
                                    .lowercase()
                            }
                        }.joinToString("/")
                        
                        // Common vendor patterns: vendor/vendor-name/package-name/src/...
                        paths.add("vendor/$vendor/$packageName/src/$restPath.php")
                        paths.add("vendor/$vendor/$packageName/$restPath.php")
                    }
                }
            }
        }

        // Standard Laravel paths
        paths.add("app/Http/Controllers/$controllerClass.php")

        return paths
    }

    private fun findMethodUsingPhpPsi(psiFile: PsiFile, methodName: String): Pair<PhpClass, Method?>? {
        val phpClasses = PsiTreeUtil.findChildrenOfType(psiFile, PhpClass::class.java)

        for (phpClass in phpClasses) {
            val method = findMethodInPhpClass(phpClass, methodName)
            if (method.second != null) {
                return method
            }
        }
        return null
    }

    private fun findMethodInPhpClass(phpClass: PhpClass, methodName: String): Pair<PhpClass, Method?> {
        val method = phpClass.findOwnMethodByName(methodName)
        if (method != null) {
            return Pair(phpClass, method)
        }

        val superClass = phpClass.superClass
        if (superClass != null) {
            val inheritedMethod = findMethodInPhpClass(superClass, methodName)
            if (inheritedMethod.second != null) {
                return inheritedMethod
            }
        }

        val traits = phpClass.traits
        for (trait in traits) {
            val traitMethod = findMethodInPhpClass(trait, methodName)
            if (traitMethod.second != null) {
                return traitMethod
            }
        }

        return Pair(phpClass, null)
    }
}