package com.rightcapital.backend.phpendpoints.toolWindow

import com.intellij.util.ui.JBUI
import java.awt.Component
import java.awt.Cursor
import javax.swing.JTable
import javax.swing.table.DefaultTableCellRenderer

/**
 * Custom renderer for Method Name column to show it's clickable
 */
class MethodNameRenderer : DefaultTableCellRenderer() {

    override fun getTableCellRendererComponent(
        table: JTable,
        value: Any?,
        isSelected: Boolean,
        hasFocus: Boolean,
        row: Int,
        column: Int
    ): Component {
        val component = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column)

        if (!isSelected && value is String && value.isNotBlank()) {
            // Make method names look like links
            foreground = JBUI.CurrentTheme.Link.Foreground.ENABLED
            cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)
            toolTipText = "Click to navigate to method: $value"
        } else if (!isSelected) {
            foreground = table.foreground
            cursor = Cursor.getDefaultCursor()
            toolTipText = null
        }

        return component
    }
}