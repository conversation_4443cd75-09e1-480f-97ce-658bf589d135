package com.rightcapital.backend.phpendpoints.toolWindow

import com.rightcapital.backend.phpendpoints.models.ApiEndpoint
import javax.swing.table.AbstractTableModel

/**
 * Table model for displaying API endpoints
 */
class EndpointTableModel : AbstractTableModel() {

    private val columnNames = arrayOf("Method", "URL", "Controller", "Method Name")
    private var endpoints = listOf<ApiEndpoint>()

    fun setEndpoints(newEndpoints: List<ApiEndpoint>) {
        endpoints = newEndpoints
        fireTableDataChanged()
    }

    fun getEndpointAt(row: Int): ApiEndpoint = endpoints[row]

    override fun getRowCount(): Int = endpoints.size

    override fun getColumnCount(): Int = columnNames.size

    override fun getColumnName(column: Int): String = columnNames[column]

    override fun getValueAt(rowIndex: Int, columnIndex: Int): Any {
        val endpoint = endpoints[rowIndex]
        return when (columnIndex) {
            0 -> endpoint.httpMethod
            1 -> endpoint.url
            2 -> endpoint.controllerClass ?: ""
            3 -> endpoint.methodName ?: ""
            else -> ""
        }
    }

    override fun getColumnClass(columnIndex: Int): Class<*> = String::class.java
}