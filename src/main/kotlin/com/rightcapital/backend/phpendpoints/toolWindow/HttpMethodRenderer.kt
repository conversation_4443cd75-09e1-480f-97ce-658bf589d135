package com.rightcapital.backend.phpendpoints.toolWindow

import com.intellij.util.ui.JBUI
import java.awt.Color
import java.awt.Component
import java.awt.Font
import javax.swing.JTable
import javax.swing.table.DefaultTableCellRenderer

/**
 * Custom renderer for HTTP methods with color coding
 */
class HttpMethodRenderer : DefaultTableCellRenderer() {

    override fun getTableCellRendererComponent(
        table: JTable,
        value: Any?,
        isSelected: Boolean,
        hasFocus: <PERSON><PERSON><PERSON>,
        row: Int,
        column: Int
    ): Component {
        val component = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column)

        if (!isSelected && value is String) {
            when (value.uppercase()) {
                "GET" -> {
                    foreground = JBUI.CurrentTheme.Link.Foreground.ENABLED
                    font = font.deriveFont(Font.BOLD)
                }

                "POST" -> {
                    foreground = JBUI.CurrentTheme.NotificationWarning.foregroundColor()
                    font = font.deriveFont(Font.BOLD)
                }

                "PUT", "PATCH" -> {
                    foreground = JBUI.CurrentTheme.NotificationInfo.foregroundColor()
                    font = font.deriveFont(Font.BOLD)
                }

                "DELETE" -> {
                    foreground = JBUI.CurrentTheme.NotificationError.foregroundColor()
                    font = font.deriveFont(Font.BOLD)
                }

                else -> {
                    foreground = table.foreground
                    font = font.deriveFont(Font.PLAIN)
                }
            }
        }

        if (!isSelected && value is String) {
            when (value.uppercase()) {
                "GET" -> {
                    foreground = Color(34, 139, 34)
                    font = font.deriveFont(Font.BOLD)
                }

                "POST" -> {
                    foreground = Color(255, 140, 0)
                    font = font.deriveFont(Font.BOLD)
                }

                "PUT", "PATCH" -> {
                    foreground = Color(30, 144, 255)
                    font = font.deriveFont(Font.BOLD)
                }

                "DELETE" -> {
                    foreground = Color(220, 20, 60)
                    font = font.deriveFont(Font.BOLD)
                }

                else -> {
                    foreground = table.foreground
                    font = font.deriveFont(Font.PLAIN)
                }
            }
        }
        return component
    }
}