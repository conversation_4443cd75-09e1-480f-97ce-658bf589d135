package com.rightcapital.backend.phpendpoints.toolWindow

import com.intellij.util.ui.JBUI
import java.awt.Component
import java.awt.Cursor
import javax.swing.JTable
import javax.swing.table.DefaultTableCellRenderer

/**
 * Custom renderer for Controller column to show it's clickable
 */
class ControllerRenderer : DefaultTableCellRenderer() {

    override fun getTableCellRendererComponent(
        table: JTable,
        value: Any?,
        isSelected: Boolean,
        hasFocus: <PERSON>olean,
        row: Int,
        column: Int
    ): Component {
        val component = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column)

        if (!isSelected && value is String && value.isNotBlank()) {
            // Make controller names look like links
            foreground = JBUI.CurrentTheme.Link.Foreground.ENABLED
            cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)
            toolTipText = "Click to open controller file: $value"
        } else if (!isSelected) {
            foreground = table.foreground
            cursor = Cursor.getDefaultCursor()
            toolTipText = null
        }

        return component
    }
}