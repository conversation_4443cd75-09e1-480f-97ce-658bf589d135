package com.rightcapital.backend.phpendpoints.settings

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.util.xmlb.XmlSerializerUtil

@Service(Service.Level.APP)
@State(
    name = "PhpEndpointsSettings",
    storages = [Storage("phpEndpointsSettings.xml")]
)
class PhpEndpointsSettings : PersistentStateComponent<PhpEndpointsSettings> {
    
    private val logger = thisLogger()
    
    var phpExecutablePath: String = detectDefaultPhpPath()
    
    companion object {
        fun getInstance(): PhpEndpointsSettings {
            return ApplicationManager.getApplication().getService(PhpEndpointsSettings::class.java)
        }
        
        /**
         * Detect the default PHP path, trying PhpStorm's configured interpreter first
         */
        private fun detectDefaultPhpPath(): String {
            return "php" // Default fallback, will be overridden by project-specific detection
        }
        
        /**
         * Get PHP interpreter path for a specific project using PhpStorm's configuration
         */
        fun getPhpInterpreterPath(project: Project?): String {
            val settings = getInstance()
            if (project == null) {
                return settings.phpExecutablePath.ifEmpty { detectOsDefaultPhpPath() }
            }
            
            return try {
                // Try to get the configured PHP interpreter from PhpStorm
                val phpInterpreterPath = getPhpStormConfiguredInterpreter(project)
                if (phpInterpreterPath != null && phpInterpreterPath.isNotEmpty()) {
                    getInstance().logger.info("Using PhpStorm configured PHP interpreter: $phpInterpreterPath")
                    return phpInterpreterPath
                }
                
                // Fallback to user settings or OS detection
                settings.phpExecutablePath.ifEmpty { detectOsDefaultPhpPath() }
            } catch (e: Exception) {
                getInstance().logger.warn("Failed to get PHP interpreter from PhpStorm configuration: ${e.message}")
                settings.phpExecutablePath.ifEmpty { detectOsDefaultPhpPath() }
            }
        }
        
        /**
         * Cache for PhpStorm API availability to avoid repeated reflection attempts
         */
        @Volatile
        private var phpStormApiAvailable: Boolean? = null
        
        /**
         * Get PHP interpreter from PhpStorm's project configuration
         */
        private fun getPhpStormConfiguredInterpreter(project: Project): String? {
            // Quick return if we already know the API is not available
            if (phpStormApiAvailable == false) {
                return null
            }
            
            return try {
                // Use reflection to access PhpStorm's PHP configuration API
                val phpConfigClass = Class.forName("com.jetbrains.php.config.PhpProjectConfigurationFacade")
                val getInstanceMethod = phpConfigClass.getMethod("getInstance", Project::class.java)
                val facade = getInstanceMethod.invoke(null, project)
                
                val getInterpreterMethod = phpConfigClass.getMethod("getInterpreter")
                val interpreter = getInterpreterMethod.invoke(facade)
                
                if (interpreter != null) {
                    val interpreterClass = interpreter.javaClass
                    
                    // Try different possible method names for getting the PHP executable path
                    val possibleMethods = listOf(
                        "getPhpExecutable",
                        "getExecutablePath", 
                        "getPath",
                        "getHome",
                        "getPhpPath",
                        "toString" // Fallback to see what's in the object
                    )
                    
                    var phpExecutable: String? = null
                    for (methodName in possibleMethods) {
                        try {
                            val method = interpreterClass.getMethod(methodName)
                            val result = method.invoke(interpreter)
                            
                            if (result is String && result.isNotEmpty()) {
                                // For toString method, try to extract path from the string
                                if (methodName == "toString") {
                                    // Try to extract a path-like string
                                    val pathPattern = Regex("""/[^"\s]+(?:php(?:\.exe)?)?""")
                                    val match = pathPattern.find(result)
                                    if (match != null) {
                                        phpExecutable = match.value
                                        getInstance().logger.info("Extracted PHP path from toString: $phpExecutable")
                                        break
                                    }
                                } else {
                                    phpExecutable = result
                                    getInstance().logger.info("Found PhpStorm configured interpreter using method '$methodName': $phpExecutable")
                                    break
                                }
                            }
                        } catch (e: NoSuchMethodException) {
                            // Method doesn't exist, try next one
                            continue
                        } catch (e: Exception) {
                            getInstance().logger.debug("Failed to invoke method '$methodName': ${e.message}")
                            continue
                        }
                    }
                    
                    if (phpExecutable == null) {
                        getInstance().logger.info("Could not find PHP executable path in interpreter object. Available methods: ${interpreterClass.methods.map { it.name }}")
                        getInstance().logger.debug("Interpreter object: $interpreter")
                    }
                    
                    // Mark API as available if we got this far
                    phpStormApiAvailable = true
                    phpExecutable
                } else {
                    getInstance().logger.info("No PHP interpreter configured in PhpStorm for project: ${project.name}")
                    phpStormApiAvailable = true
                    null
                }
            } catch (e: ClassNotFoundException) {
                getInstance().logger.info("PhpStorm PHP plugin not available")
                phpStormApiAvailable = false
                null
            } catch (e: NoSuchMethodException) {
                getInstance().logger.info("PhpStorm PHP configuration API has changed")
                phpStormApiAvailable = false
                null
            } catch (e: Exception) {
                getInstance().logger.warn("Error accessing PhpStorm PHP configuration: ${e.javaClass.simpleName}: ${e.message}")
                // Don't mark as unavailable for other exceptions, might be temporary
                null
            }
        }
        
        /**
         * Detect the default PHP path based on the operating system
         */
        private fun detectOsDefaultPhpPath(): String {
            val osName = System.getProperty("os.name").lowercase()
            
            return when {
                osName.contains("mac") -> {
                    // Try common macOS paths
                    val paths = listOf(
                        "/opt/homebrew/bin/php",     // Homebrew on Apple Silicon
                        "/usr/local/bin/php",        // Homebrew on Intel Mac
                        "/usr/bin/php"               // System PHP
                    )
                    paths.firstOrNull { java.io.File(it).exists() } ?: "php"
                }
                osName.contains("linux") -> {
                    // Try common Linux paths
                    val paths = listOf(
                        "/usr/bin/php",
                        "/usr/local/bin/php"
                    )
                    paths.firstOrNull { java.io.File(it).exists() } ?: "php"
                }
                osName.contains("windows") -> {
                    // For Windows, try to use PATH
                    "php"
                }
                else -> "php"
            }
        }
    }
    
    override fun getState(): PhpEndpointsSettings = this
    
    override fun loadState(state: PhpEndpointsSettings) {
        XmlSerializerUtil.copyBean(state, this)
        logger.info("Loaded PHP Endpoints settings: phpPath=${phpExecutablePath}")
    }
    
    /**
     * Validate if the PHP executable exists and is executable
     */
    fun isPhpExecutableValid(): Boolean {
        return try {
            val phpFile = java.io.File(phpExecutablePath)
            when {
                phpExecutablePath == "php" -> {
                    // If using just "php", try to execute it to see if it's in PATH
                    val process = ProcessBuilder("php", "--version").start()
                    process.waitFor() == 0
                }
                phpFile.exists() && phpFile.canExecute() -> true
                else -> false
            }
        } catch (e: Exception) {
            logger.warn("Error validating PHP executable: ${e.message}")
            false
        }
    }
    
    /**
     * Get the PHP version for display purposes
     */
    fun getPhpVersion(): String? {
        return try {
            val process = ProcessBuilder(phpExecutablePath, "--version").start()
            val output = process.inputStream.bufferedReader().readText()
            if (process.waitFor() == 0) {
                output.lines().firstOrNull()?.substringBefore("(")?.trim()
            } else {
                null
            }
        } catch (e: Exception) {
            logger.warn("Error getting PHP version: ${e.message}")
            null
        }
    }
}