package com.rightcapital.backend.phpendpoints.settings

import com.intellij.openapi.fileChooser.FileChooserDescriptor
import com.intellij.openapi.options.Configurable
import com.intellij.openapi.project.ProjectManager
import com.intellij.openapi.ui.TextBrowseFolderListener
import com.intellij.openapi.ui.TextFieldWithBrowseButton
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.FormBuilder
import javax.swing.JComponent
import javax.swing.JPanel
import javax.swing.SwingUtilities

class PhpEndpointsConfigurable : Configurable {
    
    private var settingsComponent: PhpEndpointsSettingsComponent? = null
    
    override fun getDisplayName(): String = "PHP Endpoints"
    
    override fun createComponent(): JComponent? {
        settingsComponent = PhpEndpointsSettingsComponent()
        return settingsComponent?.getPanel()
    }
    
    override fun isModified(): Boolean {
        val settings = PhpEndpointsSettings.getInstance()
        val component = settingsComponent ?: return false
        
        return component.getPhpExecutablePath() != settings.phpExecutablePath
    }
    
    override fun apply() {
        val settings = PhpEndpointsSettings.getInstance()
        val component = settingsComponent ?: return
        
        settings.phpExecutablePath = component.getPhpExecutablePath()
    }
    
    override fun reset() {
        val settings = PhpEndpointsSettings.getInstance()
        val component = settingsComponent ?: return
        
        component.setPhpExecutablePath(settings.phpExecutablePath)
    }
    
    override fun disposeUIResources() {
        settingsComponent = null
    }
    
    class PhpEndpointsSettingsComponent {
        private val panel: JPanel
        private val phpExecutableField: TextFieldWithBrowseButton
        private val phpVersionLabel: JBLabel
        private val phpStormConfigLabel: JBLabel
        
        init {
            // PHP Executable Path
            phpExecutableField = TextFieldWithBrowseButton()
            val descriptor = FileChooserDescriptor(true, false, false, false, false, false)
            descriptor.title = "Select PHP Executable"
            descriptor.description = "Choose the PHP executable file"
            phpExecutableField.addBrowseFolderListener(
                TextBrowseFolderListener(descriptor)
            )
            
            // Add listener to update PHP version when path changes
            phpExecutableField.textField.document.addDocumentListener(object : javax.swing.event.DocumentListener {
                override fun insertUpdate(e: javax.swing.event.DocumentEvent?) = updatePhpVersion()
                override fun removeUpdate(e: javax.swing.event.DocumentEvent?) = updatePhpVersion()
                override fun changedUpdate(e: javax.swing.event.DocumentEvent?) = updatePhpVersion()
            })
            
            // PHP Version display
            phpVersionLabel = JBLabel("PHP version will be displayed here")
            phpVersionLabel.foreground = java.awt.Color.GRAY
            
            // PhpStorm configuration display
            phpStormConfigLabel = JBLabel()
            updatePhpStormConfigInfo()
            
            // Build the form
            panel = FormBuilder.createFormBuilder()
                .addComponent(JBLabel("PhpStorm Project Configuration:"))
                .addComponent(phpStormConfigLabel)
                .addSeparator()
                .addLabeledComponent("Override PHP Executable Path:", phpExecutableField, 1, false)
                .addComponent(JBLabel("PHP Version:"))
                .addComponent(phpVersionLabel)
                .addComponentFillVertically(JPanel(), 0)
                .panel
        }
        
        fun getPanel(): JPanel = panel
        
        fun getPhpExecutablePath(): String = phpExecutableField.text.trim()
        
        fun setPhpExecutablePath(path: String) {
            phpExecutableField.text = path
            updatePhpVersion()
        }
        
        private fun updatePhpStormConfigInfo() {
            SwingUtilities.invokeLater {
                try {
                    val currentProject = ProjectManager.getInstance().openProjects.firstOrNull()
                    if (currentProject != null) {
                        val phpPath = PhpEndpointsSettings.getPhpInterpreterPath(currentProject)
                        phpStormConfigLabel.text = "Current: $phpPath"
                        phpStormConfigLabel.foreground = java.awt.Color.BLUE
                    } else {
                        phpStormConfigLabel.text = "No project open - using fallback detection"
                        phpStormConfigLabel.foreground = java.awt.Color.GRAY
                    }
                } catch (e: Exception) {
                    phpStormConfigLabel.text = "Unable to detect PhpStorm configuration"
                    phpStormConfigLabel.foreground = java.awt.Color.RED
                }
            }
        }
        
        private fun updatePhpVersion() {
            SwingUtilities.invokeLater {
                val path = phpExecutableField.text.trim()
                if (path.isNotEmpty()) {
                    try {
                        val process = ProcessBuilder(path, "--version").start()
                        val output = process.inputStream.bufferedReader().readText()
                        if (process.waitFor() == 0) {
                            val version = output.lines().firstOrNull()?.substringBefore("(")?.trim()
                            phpVersionLabel.text = version ?: "Unknown version"
                            phpVersionLabel.foreground = java.awt.Color.BLACK
                        } else {
                            phpVersionLabel.text = "Unable to get PHP version"
                            phpVersionLabel.foreground = java.awt.Color.RED
                        }
                    } catch (e: Exception) {
                        phpVersionLabel.text = "Error: ${e.message}"
                        phpVersionLabel.foreground = java.awt.Color.RED
                    }
                } else {
                    // Show current effective PHP path
                    val currentProject = ProjectManager.getInstance().openProjects.firstOrNull()
                    val effectivePath = PhpEndpointsSettings.getPhpInterpreterPath(currentProject)
                    phpVersionLabel.text = "Will use: $effectivePath"
                    phpVersionLabel.foreground = java.awt.Color.GRAY
                }
            }
        }
    }
}