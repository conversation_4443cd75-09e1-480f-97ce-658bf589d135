package com.rightcapital.backend.phpendpoints.services

import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiManager
import com.intellij.psi.search.FileTypeIndex
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.openapi.fileTypes.FileTypeManager
import com.rightcapital.backend.phpendpoints.models.ApiEndpoint
import com.rightcapital.backend.phpendpoints.models.PhpFramework
import com.rightcapital.backend.phpendpoints.settings.PhpEndpointsSettings
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import java.io.File
import java.io.BufferedReader
import java.io.InputStreamReader

@Service(Service.Level.PROJECT)
class PhpEndpointScanner(private val project: Project) {
    
    private val logger = thisLogger()
    private val endpointCache = ConcurrentHashMap<String, List<ApiEndpoint>>()
    private val parsers = listOf(
        LaravelEndpointParser(),
        SymfonyEndpointParser(),
        NativePhpEndpointParser()
    )
    
    /**
     * Scan all PHP files in the project for API endpoints
     */
    fun scanAllEndpoints(): List<ApiEndpoint> {
        logger.info("Starting PHP endpoint scan for project: ${project.name}")

        // Try Laravel artisan command first
        val framework = detectFramework()
        if (framework == PhpFramework.LARAVEL) {
            val artisanEndpoints = scanLaravelArtisan()
            if (artisanEndpoints.isNotEmpty()) {
                logger.info("Found ${artisanEndpoints.size} endpoints via artisan command")
                return artisanEndpoints
            }
        }

        // Fallback to file scanning
        val allEndpoints = mutableListOf<ApiEndpoint>()
        val phpFiles = findPhpFiles()

        logger.info("Found ${phpFiles.size} PHP files to scan")

        phpFiles.forEach { virtualFile ->
            try {
                val endpoints = scanFile(virtualFile)
                allEndpoints.addAll(endpoints)
                endpointCache[virtualFile.path] = endpoints
            } catch (e: Exception) {
                logger.warn("Error scanning file ${virtualFile.path}: ${e.message}")
            }
        }

        logger.info("Found ${allEndpoints.size} endpoints total")
        return allEndpoints.sortedBy { it.url }
    }

    /**
     * Scan Laravel routes using artisan command
     */
    private fun scanLaravelArtisan(): List<ApiEndpoint> {
        val settings = PhpEndpointsSettings.getInstance()
        
        // Get PHP interpreter path (try PhpStorm configuration first, then settings, then OS detection)
        val phpPath = PhpEndpointsSettings.getPhpInterpreterPath(project)
        
        // Check if PHP executable is valid
        if (!isPhpExecutableValid(phpPath)) {
            logger.warn("PHP executable is not valid: $phpPath")
            return emptyList()
        }
        
        val projectPath = project.basePath ?: return emptyList()
        val artisanFile = File(projectPath, "artisan")

        if (!artisanFile.exists()) {
            logger.info("artisan file not found, skipping artisan route scan")
            return emptyList()
        }

        return try {
            logger.info("Executing php artisan route:list --json command using: $phpPath")
            val processBuilder = ProcessBuilder(phpPath, "artisan", "route:list", "--json")
            processBuilder.directory(File(projectPath))
            processBuilder.redirectErrorStream(true)

            val process = processBuilder.start()
            val output = StringBuilder()

            BufferedReader(InputStreamReader(process.inputStream)).use { reader ->
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    output.append(line).append("\n")
                }
            }

            // Wait for process to complete with timeout (30 seconds default)
            val timeoutSeconds = 30L
            val finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS)
            if (!finished) {
                logger.warn("Artisan command timed out after $timeoutSeconds seconds")
                process.destroyForcibly()
                return emptyList()
            }

            val exitCode = process.exitValue()
            if (exitCode == 0) {
                parseArtisanRouteJson(output.toString())
            } else {
                logger.warn("artisan route:list command failed with exit code: $exitCode")
                logger.warn("Output: $output")
                emptyList()
            }
        } catch (e: Exception) {
            logger.warn("Error executing artisan command: ${e.message}")
            emptyList()
        }
    }

    /**
     * Parse the JSON output from php artisan route:list --json
     */
    private fun parseArtisanRouteJson(jsonOutput: String): List<ApiEndpoint> {
        val endpoints = mutableListOf<ApiEndpoint>()

        try {
            logger.info("Parsing artisan JSON output")

            // Parse the JSON array - each route is a JSON object
            // Pattern to match individual route objects in the JSON array
            val routeObjectPattern = Regex("""\{[^}]*"method":\s*"([^"]+)"[^}]*"uri":\s*"([^"]*)"[^}]*"action":\s*"([^"]*)"[^}]*\}""")

            routeObjectPattern.findAll(jsonOutput).forEach { match ->
                try {
                    val methods = match.groupValues[1]
                    val uri = match.groupValues[2]
                    val action = match.groupValues[3]

                    // Parse methods - they come as "GET|HEAD" or just "GET"
                    val methodList = methods.split("|").map { it.trim() }.filter { it.isNotBlank() }

                    // Parse controller and method from action
                    val (controllerClass, methodName, fullControllerPath) = parseActionString(action)

                    // Create endpoints for each HTTP method (but prioritize main methods)
                    val mainMethods = methodList.filter { it in listOf("GET", "POST", "PUT", "PATCH", "DELETE") }
                    val methodsToUse = if (mainMethods.isNotEmpty()) mainMethods else methodList.take(1)

                    methodsToUse.forEach { method ->
                        if (uri.isNotBlank() && method.isNotBlank()) {
                            // Unescape JSON escaped characters
                            val cleanUri = uri.replace("\\/", "/")

                            endpoints.add(
                                ApiEndpoint(
                                    url = if (cleanUri.startsWith("/")) cleanUri else "/$cleanUri",
                                    httpMethod = method,
                                    controllerClass = controllerClass,
                                    methodName = methodName,
                                    filePath = "", // We don't have file path from artisan output
                                    lineNumber = 0,
                                    framework = PhpFramework.LARAVEL,
                                    fullControllerPath = fullControllerPath
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    logger.warn("Error parsing individual route: ${e.message}")
                }
            }

            // If regex parsing didn't work well, try the more detailed parsing
            if (endpoints.size < 5) { // Assume we should have at least a few routes
                logger.info("Regex parsing found few routes, trying detailed parsing")
                val detailedEndpoints = parseJsonDetailed(jsonOutput)
                if (detailedEndpoints.size > endpoints.size) {
                    endpoints.clear()
                    endpoints.addAll(detailedEndpoints)
                }
            }

        } catch (e: Exception) {
            logger.warn("Error parsing artisan route JSON output: ${e.message}")
        }

        logger.info("Parsed ${endpoints.size} endpoints from artisan output")
        return endpoints
    }

    /**
     * More detailed JSON parsing that handles the exact Laravel format
     */
    private fun parseJsonDetailed(jsonOutput: String): List<ApiEndpoint> {
        val endpoints = mutableListOf<ApiEndpoint>()

        try {
            // Split by route objects - each starts with {"domain"
            val routeObjects = jsonOutput.split("""{"domain"""").drop(1) // Skip first empty part

            for (routeStr in routeObjects) {
                val fullRouteStr = """{"domain"""" + routeStr

                // Extract individual fields using regex
                val methodMatch = Regex(""""method":\s*"([^"]+)"""").find(fullRouteStr)
                val uriMatch = Regex(""""uri":\s*"([^"]*)" """).find(fullRouteStr)
                val actionMatch = Regex(""""action":\s*"([^"]*)" """).find(fullRouteStr)
                val nameMatch = Regex(""""name":\s*(?:"([^"]*)"|null)""").find(fullRouteStr)

                if (methodMatch != null && uriMatch != null && actionMatch != null) {
                    val methods = methodMatch.groupValues[1]
                    val uri = uriMatch.groupValues[1]
                    val action = actionMatch.groupValues[1]

                    // Parse methods - they come as "GET|HEAD" or just "GET"
                    val methodList = methods.split("|").map { it.trim() }.filter { it.isNotBlank() }

                    // Parse controller and method from action
                    val (controllerClass, methodName, fullControllerPath) = parseActionString(action)

                    // Create endpoints for each HTTP method (but prioritize main methods)
                    val mainMethods = methodList.filter { it in listOf("GET", "POST", "PUT", "PATCH", "DELETE") }
                    val methodsToUse = if (mainMethods.isNotEmpty()) mainMethods else methodList.take(1)

                    methodsToUse.forEach { method ->
                        if (uri.isNotBlank() && method.isNotBlank()) {
                            // Unescape JSON escaped characters
                            val cleanUri = uri.replace("\\/", "/")

                            endpoints.add(
                                ApiEndpoint(
                                    url = if (cleanUri.startsWith("/")) cleanUri else "/$cleanUri",
                                    httpMethod = method,
                                    controllerClass = controllerClass,
                                    methodName = methodName,
                                    filePath = "", // We don't have file path from artisan output
                                    lineNumber = 0,
                                    framework = PhpFramework.LARAVEL,
                                    fullControllerPath = fullControllerPath
                                )
                            )
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("Error in detailed JSON parsing: ${e.message}")
        }

        return endpoints
    }



    /**
     * Parse Laravel action string like "App\\Http\\Controllers\\UserController@index"
     * Returns Triple of (controllerClass, methodName, fullControllerPath)
     */
    private fun parseActionString(action: String): Triple<String?, String?, String?> {
        // First, unescape JSON escaped backslashes
        val cleanAction = action.replace("\\\\", "\\")

        return when {
            // Standard controller@method format: "App\Http\Controllers\UserController@index"
            cleanAction.contains("@") -> {
                val parts = cleanAction.split("@")
                val fullController = parts[0]
                val method = parts.getOrNull(1)

                // Extract just the controller class name from full namespace
                val controller = fullController.substringAfterLast("\\").substringAfterLast("/")
                Triple(controller, method, fullController)
            }
            // Invokable controller or class name only
            cleanAction.contains("\\") -> {
                val controller = cleanAction.substringAfterLast("\\").substringAfterLast("/")
                Triple(controller, "__invoke", cleanAction)
            }
            // Closure routes
            cleanAction == "Closure" -> Triple(null, "Closure", null)
            // Other formats
            cleanAction.isNotBlank() -> {
                // Could be a simple controller name or other format
                Triple(cleanAction, null, null)
            }
            else -> Triple(null, null, null)
        }
    }
    
    /**
     * Scan a specific file for endpoints
     */
    fun scanFile(virtualFile: VirtualFile): List<ApiEndpoint> {
        val psiManager = PsiManager.getInstance(project)
        val psiFile = psiManager.findFile(virtualFile) ?: return emptyList()
        
        return parsers.flatMap { parser ->
            try {
                parser.parseEndpoints(psiFile, virtualFile)
            } catch (e: Exception) {
                logger.warn("Parser ${parser.javaClass.simpleName} failed for ${virtualFile.path}: ${e.message}")
                emptyList()
            }
        }
    }

    /**
     * Find all PHP files in the project
     */
    private fun findPhpFiles(): Collection<VirtualFile> {
        val phpFileType = FileTypeManager.getInstance().getFileTypeByExtension("php")
        return FileTypeIndex.getFiles(phpFileType, GlobalSearchScope.projectScope(project))
    }
    
    /**
     * Validate if the PHP executable exists and is executable
     */
    private fun isPhpExecutableValid(phpPath: String): Boolean {
        return try {
            val phpFile = java.io.File(phpPath)
            when {
                phpPath == "php" -> {
                    // If using just "php", try to execute it to see if it's in PATH
                    val process = ProcessBuilder("php", "--version").start()
                    process.waitFor() == 0
                }
                phpFile.exists() && phpFile.canExecute() -> true
                else -> false
            }
        } catch (e: Exception) {
            logger.warn("Error validating PHP executable: ${e.message}")
            false
        }
    }
    
    /**
     * Detect the PHP framework used in the project
     */
    fun detectFramework(): PhpFramework {
        return try {
            val projectRoot = project.baseDir ?: return PhpFramework.UNKNOWN
            
            // Use a more defensive approach to avoid triggering PhpStorm's internal framework detection
            val framework = when {
                // Check for Laravel first (most common)
                isLaravelProject(projectRoot) -> PhpFramework.LARAVEL
                // Check for Symfony (be more careful to avoid XML parsing issues)
                isSymfonyProject(projectRoot) -> PhpFramework.SYMFONY
                // Check composer.json safely
                else -> hasFrameworkInComposer(projectRoot) ?: PhpFramework.UNKNOWN
            }
            
            logger.info("Detected framework: $framework for project: ${project.name}")
            framework
        } catch (e: Exception) {
            logger.warn("Error detecting framework: ${e.message}")
            PhpFramework.UNKNOWN
        }
    }
    
    private fun isLaravelProject(projectRoot: com.intellij.openapi.vfs.VirtualFile): Boolean {
        return projectRoot.findChild("artisan") != null
    }
    
    private fun isSymfonyProject(projectRoot: com.intellij.openapi.vfs.VirtualFile): Boolean {
        // Check for multiple Symfony indicators, not just symfony.lock
        return projectRoot.findChild("symfony.lock") != null ||
               projectRoot.findChild("bin")?.findChild("console") != null ||
               projectRoot.findChild("config")?.findChild("services.yaml") != null
    }
    
    private fun hasFrameworkInComposer(projectRoot: com.intellij.openapi.vfs.VirtualFile): PhpFramework? {
        return try {
            val composerFile = projectRoot.findChild("composer.json") ?: return null
            val content = String(composerFile.contentsToByteArray())
            
            when {
                content.contains("\"laravel/framework\"") -> PhpFramework.LARAVEL
                content.contains("\"symfony/symfony\"") || content.contains("\"symfony/framework-bundle\"") -> PhpFramework.SYMFONY
                content.contains("\"codeigniter/framework\"") -> PhpFramework.CODEIGNITER
                content.contains("\"slim/slim\"") -> PhpFramework.SLIM
                else -> null
            }
        } catch (e: Exception) {
            logger.warn("Error reading composer.json: ${e.message}")
            null
        }
    }
}

/**
 * Base interface for endpoint parsers
 */
interface EndpointParser {
    fun parseEndpoints(psiFile: PsiFile, virtualFile: VirtualFile): List<ApiEndpoint>
    fun canParse(psiFile: PsiFile): Boolean
}

/**
 * Laravel-specific endpoint parser
 */
class LaravelEndpointParser : EndpointParser {
    override fun canParse(psiFile: PsiFile): Boolean {
        val fileName = psiFile.name
        return fileName.contains("routes") || fileName.contains("Route") || 
               psiFile.text.contains("Route::")
    }
    
    override fun parseEndpoints(psiFile: PsiFile, virtualFile: VirtualFile): List<ApiEndpoint> {
        if (!canParse(psiFile)) return emptyList()

        val endpoints = mutableListOf<ApiEndpoint>()
        val content = psiFile.text
        val lines = content.lines()

        // Parse various Laravel route patterns
        parseBasicRoutes(lines, virtualFile, endpoints)
        parseArrayRoutes(lines, virtualFile, endpoints)
        parseResourceRoutes(lines, virtualFile, endpoints)
        parseGroupRoutes(lines, virtualFile, endpoints)

        return endpoints
    }

    private fun parseBasicRoutes(lines: List<String>, virtualFile: VirtualFile, endpoints: MutableList<ApiEndpoint>) {
        // Pattern 1: Route::get('/path', 'Controller@method')
        val basicPattern1 = Regex("""Route::(\w+)\s*\(\s*['"]([^'"]+)['"](?:\s*,\s*['"]([^'"@]+@[^'"]+)['"])?\s*\)""")

        // Pattern 2: Route::get('/path', [Controller::class, 'method'])
        val basicPattern2 = Regex("""Route::(\w+)\s*\(\s*['"]([^'"]+)['"](?:\s*,\s*\[([^:]+)::class\s*,\s*['"]([^'"]+)['"])\s*\)""")

        // Pattern 3: Route::get('/path', function() {...})
        val closurePattern = Regex("""Route::(\w+)\s*\(\s*['"]([^'"]+)['"](?:\s*,\s*function\s*\()""")

        lines.forEachIndexed { index, line ->
            // Basic pattern with Controller@method
            basicPattern1.findAll(line).forEach { match ->
                val method = match.groupValues[1].uppercase()
                val url = match.groupValues[2]
                val controller = match.groupValues.getOrNull(3)

                val (controllerClass, methodName) = parseControllerAction(controller)

                endpoints.add(createEndpoint(url, method, controllerClass, methodName, virtualFile, index + 1))
            }

            // Array syntax pattern
            basicPattern2.findAll(line).forEach { match ->
                val method = match.groupValues[1].uppercase()
                val url = match.groupValues[2]
                val controllerClass = match.groupValues[3]
                val methodName = match.groupValues[4]

                endpoints.add(createEndpoint(url, method, controllerClass, methodName, virtualFile, index + 1))
            }

            // Closure pattern
            closurePattern.findAll(line).forEach { match ->
                val method = match.groupValues[1].uppercase()
                val url = match.groupValues[2]

                endpoints.add(createEndpoint(url, method, null, "Closure", virtualFile, index + 1))
            }
        }
    }

    private fun parseArrayRoutes(lines: List<String>, virtualFile: VirtualFile, endpoints: MutableList<ApiEndpoint>) {
        // Pattern for routes defined with match method: Route::match(['GET', 'POST'], '/path', ...)
        val matchPattern = Regex("""Route::match\s*\(\s*\[([^\]]+)\]\s*,\s*['"]([^'"]+)['"](?:\s*,\s*['"]?([^'"]+)['"]?)?\s*\)""")

        lines.forEachIndexed { index, line ->
            matchPattern.findAll(line).forEach { match ->
                val methodsStr = match.groupValues[1]
                val url = match.groupValues[2]
                val controller = match.groupValues.getOrNull(3)

                // Parse multiple methods
                val methods = methodsStr.split(",").map { it.trim().replace("'", "").replace("\"", "") }
                val (controllerClass, methodName) = parseControllerAction(controller)

                methods.forEach { method ->
                    endpoints.add(createEndpoint(url, method.uppercase(), controllerClass, methodName, virtualFile, index + 1))
                }
            }
        }
    }

    private fun parseResourceRoutes(lines: List<String>, virtualFile: VirtualFile, endpoints: MutableList<ApiEndpoint>) {
        // Pattern for resource routes: Route::resource('users', UserController::class)
        val resourcePattern = Regex("""Route::resource\s*\(\s*['"]([^'"]+)['"](?:\s*,\s*([^:]+)::class)?\s*\)""")

        lines.forEachIndexed { index, line ->
            resourcePattern.findAll(line).forEach { match ->
                val resource = match.groupValues[1]
                val controllerClass = match.groupValues.getOrNull(2)

                // Generate standard resource routes
                val resourceRoutes = listOf(
                    Triple("GET", "/$resource", "index"),
                    Triple("GET", "/$resource/create", "create"),
                    Triple("POST", "/$resource", "store"),
                    Triple("GET", "/$resource/{id}", "show"),
                    Triple("GET", "/$resource/{id}/edit", "edit"),
                    Triple("PUT", "/$resource/{id}", "update"),
                    Triple("PATCH", "/$resource/{id}", "update"),
                    Triple("DELETE", "/$resource/{id}", "destroy")
                )

                resourceRoutes.forEach { (method, url, methodName) ->
                    endpoints.add(createEndpoint(url, method, controllerClass, methodName, virtualFile, index + 1))
                }
            }
        }
    }

    private fun parseGroupRoutes(lines: List<String>, virtualFile: VirtualFile, endpoints: MutableList<ApiEndpoint>) {
        // This is a simplified approach - in reality, route groups are more complex
        // Pattern for route groups with prefix: Route::prefix('api')->group(function () {
        val groupPattern = Regex("""Route::prefix\s*\(\s*['"]([^'"]+)['"]""")

        var currentPrefix = ""
        lines.forEachIndexed { index, line ->
            groupPattern.find(line)?.let { match ->
                currentPrefix = match.groupValues[1]
            }

            // Reset prefix when group ends (simplified)
            if (line.contains("});") && currentPrefix.isNotEmpty()) {
                currentPrefix = ""
            }
        }
    }

    private fun createEndpoint(url: String, method: String, controllerClass: String?, methodName: String?, virtualFile: VirtualFile, lineNumber: Int): ApiEndpoint {
        return ApiEndpoint(
            url = url,
            httpMethod = method,
            controllerClass = controllerClass,
            methodName = methodName,
            filePath = virtualFile.path,
            lineNumber = lineNumber,
            framework = PhpFramework.LARAVEL,
            fullControllerPath = null // For file-based parsing, we don't have full namespace path
        )
    }
    
    private fun parseControllerAction(controller: String?): Pair<String?, String?> {
        if (controller.isNullOrBlank()) return null to null

        return when {
            // Traditional Laravel syntax: 'UserController@index'
            controller.contains("@") -> {
                val parts = controller.split("@")
                parts[0] to parts.getOrNull(1)
            }
            // Array syntax already handled in parseBasicRoutes
            controller.contains("::class") -> {
                val className = controller.substringBefore("::class")
                className to null
            }
            // Invokable controller or other formats
            else -> {
                controller to null
            }
        }
    }
}

/**
 * Symfony-specific endpoint parser
 */
class SymfonyEndpointParser : EndpointParser {
    override fun canParse(psiFile: PsiFile): Boolean {
        return psiFile.text.contains("@Route") || psiFile.text.contains("#[Route")
    }
    
    override fun parseEndpoints(psiFile: PsiFile, virtualFile: VirtualFile): List<ApiEndpoint> {
        if (!canParse(psiFile)) return emptyList()
        
        val endpoints = mutableListOf<ApiEndpoint>()
        val content = psiFile.text
        val lines = content.lines()
        
        // Parse Symfony annotations: @Route("/api/users", methods={"GET"})
        val annotationPattern = Regex("""@Route\s*\(\s*['"]([^'"]+)['"](?:.*methods\s*=\s*\{['"](\w+)['"].*\})?\s*\)""")
        
        lines.forEachIndexed { index, line ->
            annotationPattern.findAll(line).forEach { match ->
                val url = match.groupValues[1]
                val method = match.groupValues.getOrNull(2)?.uppercase() ?: "GET"
                
                endpoints.add(
                    ApiEndpoint(
                        url = url,
                        httpMethod = method,
                        controllerClass = extractClassName(psiFile),
                        methodName = findNextMethodName(lines, index),
                        filePath = virtualFile.path,
                        lineNumber = index + 1,
                        framework = PhpFramework.SYMFONY,
                        fullControllerPath = null
                    )
                )
            }
        }
        
        return endpoints
    }
    
    private fun extractClassName(psiFile: PsiFile): String? {
        val classPattern = Regex("""class\s+(\w+)""")
        return classPattern.find(psiFile.text)?.groupValues?.get(1)
    }
    
    private fun findNextMethodName(lines: List<String>, startIndex: Int): String? {
        for (i in (startIndex + 1) until lines.size) {
            val methodPattern = Regex("""function\s+(\w+)\s*\(""")
            val match = methodPattern.find(lines[i])
            if (match != null) {
                return match.groupValues[1]
            }
        }
        return null
    }
}

/**
 * Native PHP endpoint parser
 */
class NativePhpEndpointParser : EndpointParser {
    override fun canParse(psiFile: PsiFile): Boolean {
        return psiFile.name.endsWith(".php")
    }
    
    override fun parseEndpoints(psiFile: PsiFile, virtualFile: VirtualFile): List<ApiEndpoint> {
        val endpoints = mutableListOf<ApiEndpoint>()
        val content = psiFile.text
        val lines = content.lines()
        
        // Look for $_SERVER['REQUEST_METHOD'] checks
        val methodCheckPattern = Regex("""\${'$'}_SERVER\s*\[\s*['"]REQUEST_METHOD['"]\s*\]\s*==\s*['"](\w+)['"]""")
        
        lines.forEachIndexed { index, line ->
            methodCheckPattern.findAll(line).forEach { match ->
                val method = match.groupValues[1].uppercase()
                
                // Try to infer URL from file path or $_SERVER['REQUEST_URI']
                val url = inferUrlFromFile(virtualFile) ?: "/unknown"
                
                endpoints.add(
                    ApiEndpoint(
                        url = url,
                        httpMethod = method,
                        controllerClass = null,
                        methodName = psiFile.name,
                        filePath = virtualFile.path,
                        lineNumber = index + 1,
                        framework = PhpFramework.NATIVE,
                        fullControllerPath = null
                    )
                )
            }
        }
        
        return endpoints
    }
    
    private fun inferUrlFromFile(virtualFile: VirtualFile): String? {
        val path = virtualFile.path
        val webRoot = findWebRoot(path)
        return if (webRoot != null) {
            path.substringAfter(webRoot).replace("\\", "/")
        } else {
            "/" + virtualFile.nameWithoutExtension
        }
    }
    
    private fun findWebRoot(path: String): String? {
        val commonWebRoots = listOf("public", "www", "htdocs", "web")
        return commonWebRoots.find { path.contains("/$it/") }?.let { "/$it/" }
    }
}
