package com.rightcapital.backend.phpendpoints.lineMarkers

import com.intellij.codeInsight.daemon.RelatedItemLineMarkerInfo
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerProvider
import com.intellij.codeInsight.navigation.NavigationGutterIconBuilder
import com.intellij.icons.AllIcons
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiManager
import com.jetbrains.php.lang.psi.elements.Method
import com.jetbrains.php.lang.psi.elements.PhpClass

class SnapshotLineMarkerProvider : RelatedItemLineMarkerProvider() {
    
    override fun collectNavigationMarkers(
        element: PsiElement,
        result: MutableCollection<in RelatedItemLineMarkerInfo<*>>
    ) {
        // Only process PHP methods
        if (element !is Method) return
        
        val containingClass = element.containingClass ?: return
        
        // Check if this is a test class (namespace start with "Test")
        if (!isTestClass(containingClass)) return
        
        // Check if the method name starts with "test"
        val methodName = element.name
        if (!methodName.startsWith("test")) return
        
        // Find the snapshot file
        val snapshotFiles = findSnapshotFile(containingClass, methodName)

        if (snapshotFiles != null && snapshotFiles.isNotEmpty()) {
            val virtualFiles = snapshotFiles.map { it -> PsiManager.getInstance(element.project).findFile(it) }
                .filter { it -> it != null }

            // Create PSI element for the snapshot file
            if (virtualFiles.isNotEmpty()) {
                val builder = NavigationGutterIconBuilder
                    .create(AllIcons.FileTypes.Json)
                    .setTargets(virtualFiles)
                    .setTooltipText("Navigate to snapshot file")
                result.add(builder.createLineMarkerInfo(element.nameIdentifier ?: element))
            }
        }
    }
    
    private fun isTestClass(phpClass: PhpClass): Boolean {
        val namespaceName = phpClass.namespaceName
        return namespaceName.startsWith("\\Test")
    }
    
    private fun findSnapshotFile(testClass: PhpClass, methodName: String): MutableList<VirtualFile>? {
        try {
            // Get the test file path
            val testFile = testClass.containingFile?.virtualFile ?: return null
            val testFilePath = testFile.path

            // Determine test type and extract relative path
            val relativePath = testFilePath.substring(testFilePath.indexOf("/tests/")+ "/tests/".length)
            val testType = relativePath.split('/')[0];

            
            // Remove .php extension
            val relativePathWithoutExtension = if (relativePath.endsWith(".php")) {
                val temp = relativePath.substring(0, relativePath.length - 4)
                temp.substring(temp.split('/')[0].length + 1)
            } else {
                relativePath
            }
            
            // Convert test method name (e.g., testGetFileListWithFolderEmpty -> GetFileListWithFolderEmpty)
            val snapshotMethodName = if (methodName.startsWith("test")) {
                methodName.substring(4) // Remove "test" prefix
            } else {
                methodName
            }
            
            // Build the snapshot file path
            val projectBasePath = testClass.project.basePath
            val snapshotPathPrefixes = listOf(
                "realworld",
                "realworld-minimal",
                "realworld-minimal-single",
                "realworld-single",
                "simple",
                "simple-minimal-single"
            )
            val snapshotPaths = snapshotPathPrefixes.map { prefix ->
                "$projectBasePath/tests/$testType/_baseline/$prefix/$relativePathWithoutExtension/$snapshotMethodName.json"
            }
            val results = mutableListOf<VirtualFile>()
            for (path in snapshotPaths) {
                val file = LocalFileSystem.getInstance().findFileByPath(path)
                if (file != null && file.exists()) {
                    results.add(file)
                }
            }
            return results;
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

}