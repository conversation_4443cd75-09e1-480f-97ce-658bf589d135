package com.rightcapital.backend.phpendpoints.lineMarkers

import com.intellij.codeInsight.daemon.RelatedItemLineMarkerInfo
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerProvider
import com.intellij.codeInsight.navigation.NavigationGutterIconBuilder
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiManager
import com.intellij.psi.search.FileTypeIndex
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.psi.util.PsiTreeUtil
import com.intellij.icons.AllIcons
import com.jetbrains.php.lang.psi.elements.Method
import com.jetbrains.php.lang.psi.elements.PhpClass
import com.rightcapital.backend.phpendpoints.services.PhpEndpointScanner

class EloquentEventLineMarkerProvider : RelatedItemLineMarkerProvider() {
    
    companion object {
        // Common Eloquent event names
        private val ELOQUENT_EVENTS = setOf(
            "creating", "created",
            "updating", "updated", 
            "saving", "saved",
            "deleting", "deleted",
            "restoring", "restored",
            "retrieved",
            "forceDeleting", "forceDeleted",
            "replicating"
        )
        
        // Map event methods to controller actions
        private val EVENT_TO_CONTROLLER_ACTION = mapOf(
            "creating" to listOf("store", "create"),
            "created" to listOf("store"),
            "updating" to listOf("update", "edit"),
            "updated" to listOf("update"),
            "saving" to listOf("store", "update"),
            "saved" to listOf("store", "update"),
            "deleting" to listOf("destroy", "delete"),
            "deleted" to listOf("destroy"),
            "restoring" to listOf("restore"),
            "restored" to listOf("restore")
        )
    }
    
    override fun collectNavigationMarkers(
        element: PsiElement,
        result: MutableCollection<in RelatedItemLineMarkerInfo<*>>
    ) {
        // Only process PHP methods
        if (element !is Method) return
        
        val containingClass = element.containingClass ?: return
        
        // Check if this is an Eloquent event class (ends with "Event" or in Events namespace)
        if (!isEloquentEventClass(containingClass)) return
        
        // Check if the method name matches Eloquent event patterns
        val methodName = element.name
        if (!ELOQUENT_EVENTS.contains(methodName)) return
        
        // Extract model name from class name (e.g., AccountEvent -> Account)
        val className = containingClass.name ?: return
        val modelName = extractModelName(className)
        
        // Find targets immediately
        val project = element.project
        
        // Find targets
        val targets = findRelatedControllerMethods(project, modelName, methodName)
        
        // Always create builder with targets (even if empty)
        val builder = NavigationGutterIconBuilder
            .create(AllIcons.Nodes.Class)
            .setTargets(targets)
            .setTooltipText(if (targets.isEmpty()) {
                "No controllers found for $modelName"
            } else {
                "Navigate to $modelName controller ($methodName event)"
            })
        
        result.add(builder.createLineMarkerInfo(element.nameIdentifier ?: element))
    }
    
    
    private fun isEloquentEventClass(phpClass: PhpClass): Boolean {
        val className = phpClass.name ?: return false
        val namespace = phpClass.namespaceName
        
        // Check if class ends with "Event" or is in Events namespace
        return className.endsWith("Event") || 
               namespace.contains("\\Events\\") || 
               namespace.endsWith("\\Events")
    }
    
    private fun extractModelName(eventClassName: String): String {
        // Remove "Event" suffix if present
        return if (eventClassName.endsWith("Event")) {
            eventClassName.substring(0, eventClassName.length - 5)
        } else {
            eventClassName
        }
    }
    
    private fun findRelatedControllerMethods(
        project: Project,
        modelName: String,
        eventName: String
    ): List<PsiElement> {
        val targets = mutableListOf<PsiElement>()
        
        // First try: Find by controller name patterns
        targets.addAll(findByControllerName(project, modelName, eventName))
        
        // Fallback: If no targets found, try to find through URL endpoints
        if (targets.isEmpty()) {
            targets.addAll(findByUrlEndpoints(project, modelName, eventName))
        }
        
        return targets
    }
    
    private fun findByControllerName(
        project: Project,
        modelName: String,
        eventName: String
    ): List<PsiElement> {
        val targets = mutableListOf<PsiElement>()
        
        try {
            // Possible controller names for the model
            val controllerNames = listOf(
                "${modelName}Controller",
                "${modelName}sController",
                modelName.pluralize() + "Controller"
            )
            
            // Get the action names that might handle this event
            val actionNames = EVENT_TO_CONTROLLER_ACTION[eventName] ?: listOf()
            
            // Search for PHP files in the project
            val scope = GlobalSearchScope.projectScope(project)
            val phpFiles = FileTypeIndex.getFiles(
                com.intellij.openapi.fileTypes.FileTypeManager.getInstance().getFileTypeByExtension("php"),
                scope
            )
            
            for (virtualFile in phpFiles) {
                // Skip non-controller files
                if (!virtualFile.name.endsWith("Controller.php")) continue
                
                // Check if this might be our controller
                val fileName = virtualFile.nameWithoutExtension
                if (!controllerNames.contains(fileName)) continue
                
                val psiFile = PsiManager.getInstance(project).findFile(virtualFile) ?: continue
                if (!psiFile.isValid) continue
                
                val phpClasses = PsiTreeUtil.findChildrenOfType(psiFile, PhpClass::class.java)
                
                for (phpClass in phpClasses) {
                    if (!phpClass.isValid) continue
                    
                    // Look for matching action methods
                    for (actionName in actionNames) {
                        val method = phpClass.findMethodByName(actionName)
                        if (method != null && method.isValid) {
                            targets.add(method)
                        }
                    }
                    
                    // Also look for methods that might handle this specific model
                    // e.g., updateAccount, deleteAccount, etc.
                    val specificMethodName = actionNames.firstOrNull()?.let { action ->
                        action + modelName
                    }
                    if (specificMethodName != null) {
                        val method = phpClass.findMethodByName(specificMethodName)
                        if (method != null && method.isValid) {
                            targets.add(method)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // Log error but don't crash
            e.printStackTrace()
        }
        
        return targets
    }
    
    private fun findByUrlEndpoints(
        project: Project,
        modelName: String,
        eventName: String
    ): List<PsiElement> {
        val targets = mutableListOf<PsiElement>()
        
        try {
            // Get the endpoint scanner service
            val scanner = project.service<PhpEndpointScanner>()
            
            // Scan all endpoints
            val endpoints = scanner.scanAllEndpoints()
            
            // Map event names to HTTP methods
            val httpMethods = when (eventName) {
                "creating", "created", "saving", "saved" -> listOf("POST")
                "updating", "updated" -> listOf("PUT", "PATCH")
                "deleting", "deleted" -> listOf("DELETE")
                "restoring", "restored" -> listOf("POST", "PUT", "PATCH")
                else -> listOf("GET", "POST", "PUT", "PATCH", "DELETE")
            }
            
            // Generate various possible URL patterns for this model
            val urlPatterns = generateUrlPatterns(modelName)
            
            for (endpoint in endpoints) {
                // Check if the URL matches any of our patterns
                val urlLower = endpoint.url.lowercase()
                val matches = urlPatterns.any { pattern ->
                    urlLower.contains(pattern)
                }
                
                if (!matches) {
                    continue
                }
                
                // Check if HTTP method matches
                if (!httpMethods.contains(endpoint.httpMethod.uppercase())) {
                    continue
                }
                
                // Try to find the controller method
                if (!endpoint.controllerClass.isNullOrBlank() && !endpoint.methodName.isNullOrBlank()) {
                    val controllerFile = findControllerFile(project, endpoint.controllerClass, endpoint.fullControllerPath)
                    if (controllerFile != null && controllerFile.isValid) {
                        val psiFile = PsiManager.getInstance(project).findFile(controllerFile) ?: continue
                        if (!psiFile.isValid) continue
                        
                        val phpClasses = PsiTreeUtil.findChildrenOfType(psiFile, PhpClass::class.java)
                        
                        for (phpClass in phpClasses) {
                            if (!phpClass.isValid) continue
                            
                            val method = phpClass.findMethodByName(endpoint.methodName)
                            if (method != null && method.isValid && !targets.contains(method)) {
                                targets.add(method)
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // Log error but don't crash
            e.printStackTrace()
        }
        
        return targets
    }
    
    private fun generateUrlPatterns(modelName: String): List<String> {
        val patterns = mutableListOf<String>()
        
        // Convert PascalCase to snake_case
        val snakeCase = modelName.toSnakeCase()
        val snakeCasePlural = modelName.pluralize().toSnakeCase()
        
        // Add various patterns
        patterns.add(snakeCase)                    // account
        patterns.add(snakeCasePlural)              // accounts
        patterns.add(modelName.lowercase())         // account (simple lowercase)
        patterns.add(modelName.pluralize().lowercase()) // accounts (simple lowercase plural)
        
        // Also handle compound names like PlanningAccessTemplate
        // Split by capitals and create variations
        val words = modelName.split(Regex("(?=[A-Z])")).filter { it.isNotEmpty() }
        if (words.size > 1) {
            // Join with underscores: planning_access_template
            val compound = words.joinToString("_") { it.lowercase() }
            patterns.add(compound)
            
            // Pluralize last word: planning_access_templates
            val compoundPlural = words.dropLast(1).joinToString("_") { it.lowercase() } + 
                                 "_" + words.last().pluralize().lowercase()
            patterns.add(compoundPlural)
            
            // Join with hyphens (sometimes used): planning-access-template
            val hyphenated = words.joinToString("-") { it.lowercase() }
            patterns.add(hyphenated)
            
            // Hyphenated plural: planning-access-templates
            val hyphenatedPlural = words.dropLast(1).joinToString("-") { it.lowercase() } + 
                                   "-" + words.last().pluralize().lowercase()
            patterns.add(hyphenatedPlural)
        }
        
        return patterns.distinct()
    }
    
    private fun String.toSnakeCase(): String {
        return this.replace(Regex("([a-z])([A-Z])"), "$1_$2")
                   .replace(Regex("([A-Z]+)([A-Z][a-z])"), "$1_$2")
                   .lowercase()
    }
    
    private fun findControllerFile(
        project: Project,
        controllerClass: String,
        fullControllerPath: String?
    ): com.intellij.openapi.vfs.VirtualFile? {
        val paths = mutableListOf<String>()
        
        // Use full controller path if available
        if (!fullControllerPath.isNullOrBlank()) {
            val namespacePath = fullControllerPath
                .replace("\\", "/")
                .replace("App/Http/Controllers", "app/Http/Controllers")
            paths.add("$namespacePath.php")
        }
        
        // Standard Laravel path
        paths.add("app/Http/Controllers/$controllerClass.php")
        
        // Try to find the file
        for (path in paths) {
            val fullPath = project.basePath + "/" + path
            val virtualFile = com.intellij.openapi.vfs.LocalFileSystem.getInstance().findFileByPath(fullPath)
            if (virtualFile != null && virtualFile.exists()) {
                return virtualFile
            }
        }
        
        return null
    }
    
    // Simple pluralization helper
    private fun String.pluralize(): String {
        return when {
            this.endsWith("y") -> this.dropLast(1) + "ies"
            this.endsWith("s") || this.endsWith("x") || this.endsWith("ch") || this.endsWith("sh") -> this + "es"
            else -> this + "s"
        }
    }
}