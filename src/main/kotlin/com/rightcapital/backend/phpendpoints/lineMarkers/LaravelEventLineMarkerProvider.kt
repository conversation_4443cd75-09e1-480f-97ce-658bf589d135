package com.rightcapital.backend.phpendpoints.lineMarkers

import com.intellij.codeInsight.daemon.RelatedItemLineMarkerInfo
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerProvider
import com.intellij.codeInsight.navigation.NavigationGutterIconBuilder
import com.intellij.icons.AllIcons
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiManager
import com.intellij.psi.search.FileTypeIndex
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.psi.util.PsiTreeUtil
import com.intellij.psi.search.searches.ReferencesSearch
import com.jetbrains.php.lang.psi.elements.*
import com.jetbrains.php.PhpIndex

class LaravelEventLineMarkerProvider : RelatedItemLineMarkerProvider() {
    
    override fun collectNavigationMarkers(
        element: PsiElement,
        result: MutableCollection<in RelatedItemLineMarkerInfo<*>>
    ) {
        // Handle event() function calls
        handleEventCalls(element, result)
        
        // Handle listener handle() methods
        handleListenerMethods(element, result)
    }
    
    private fun handleEventCalls(
        element: PsiElement,
        result: MutableCollection<in RelatedItemLineMarkerInfo<*>>
    ) {
        // Check if this is a function call
        if (element !is FunctionReference) return
        
        // Check if this is an event() call
        val functionName = element.name ?: return
        if (functionName != "event") return
        
        // Get the event class from the argument
        val eventClass = extractEventClassFromCall(element) ?: return
        
        // Find all listeners for this event
        val listeners = findEventListeners(element.project, eventClass)
        
        if (listeners.isNotEmpty()) {
            val builder = NavigationGutterIconBuilder
                .create(AllIcons.Actions.Lightning)
                .setTargets(listeners)
                .setTooltipText("Navigate to event listeners (${listeners.size} listener${if (listeners.size > 1) "s" else ""})")
            
            result.add(builder.createLineMarkerInfo(element))
        }
    }
    
    private fun handleListenerMethods(
        element: PsiElement,
        result: MutableCollection<in RelatedItemLineMarkerInfo<*>>
    ) {
        // Check if this is a method
        if (element !is Method) return
        
        // Check if this is a handle() method
        if (element.name != "handle") return
        
        // Check if this is in a Listener class
        val containingClass = element.containingClass ?: return
        if (!isListenerClass(containingClass)) return
        
        // Get the event class from the parameter
        val eventClass = extractEventClassFromHandleMethod(element) ?: return
        
        // Find all publishers of this event
        val publishers = findEventPublishers(element.project, eventClass)
        
        if (publishers.isNotEmpty()) {
            val builder = NavigationGutterIconBuilder
                .create(AllIcons.Actions.Lightning)
                .setTargets(publishers)
                .setTooltipText("Navigate to event publishers (${publishers.size} publisher${if (publishers.size > 1) "s" else ""})")
            
            result.add(builder.createLineMarkerInfo(element.nameIdentifier ?: element))
        }
    }
    
    private fun extractEventClassFromCall(functionCall: FunctionReference): String? {
        // Get the first parameter
        val parameters = functionCall.parameters
        if (parameters.isEmpty()) return null
        
        val firstParam = parameters[0]
        
        // Handle new EventClass($params) pattern
        if (firstParam is NewExpression) {
            val classRef = firstParam.classReference ?: return null
            return classRef.name
        }
        
        // Handle EventClass::dispatch() or other static calls
        if (firstParam is MethodReference) {
            val classRef = firstParam.classReference ?: return null
            return classRef.name
        }
        
        return null
    }
    
    private fun extractEventClassFromHandleMethod(method: Method): String? {
        // Get the first parameter type
        val parameters = method.parameters
        if (parameters.isEmpty()) return null
        
        val firstParam = parameters[0]
        val typeDeclaration = firstParam.declaredType
        
        return typeDeclaration?.toString()?.replace("\\\\", "\\")
    }
    
    private fun findEventListeners(project: Project, eventClass: String): List<PsiElement> {
        val listeners = mutableListOf<PsiElement>()
        
        try {
            // Search for listener files
            val scope = GlobalSearchScope.projectScope(project)
            val phpFiles = FileTypeIndex.getFiles(
                com.intellij.openapi.fileTypes.FileTypeManager.getInstance().getFileTypeByExtension("php"),
                scope
            )

            for (virtualFile in phpFiles) {
                // Check if file is in Listeners directory
                if (!virtualFile.path.contains("/Listeners/")) continue

                val psiFile = PsiManager.getInstance(project).findFile(virtualFile) ?: continue
                if (!psiFile.isValid) continue

                // Find all classes in the file
                val phpClasses = PsiTreeUtil.findChildrenOfType(psiFile, PhpClass::class.java)
                
                for (phpClass in phpClasses) {
                    if (!phpClass.isValid) continue
                    
                    // Find handle method
                    val handleMethod = phpClass.findMethodByName("handle")
                    if (handleMethod != null && handleMethod.isValid) {
                        // Check if this handle method accepts our event
                        val eventType = extractEventClassFromHandleMethod(handleMethod)
                        if (eventType == eventClass || eventType?.endsWith("\\" + eventClass.substringAfterLast("\\")) == true) {
                            listeners.add(handleMethod)
                        }
                    }
                }
            }
            
            // Also check EventServiceProvider for registered listeners
            listeners.addAll(findListenersFromEventServiceProvider(project, eventClass))

        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return listeners
    }
    
    private fun findEventPublishers(project: Project, eventClass: String): List<PsiElement> {
        val publishers = mutableListOf<PsiElement>()
        
        try {
            val eventClassName = eventClass.substringAfterLast("\\")
            
            // Find the event class PSI element using PhpIndex
            val phpIndex = PhpIndex.getInstance(project)
            val eventClasses = phpIndex.getClassesByFQN(eventClass)
            
            if (eventClasses.isNotEmpty()) {
                val eventClassPsi = eventClasses.first()
                
                // Use ReferencesSearch to find all references to this class
                val references = ReferencesSearch.search(eventClassPsi, GlobalSearchScope.projectScope(project))
                
                for (reference in references) {
                    val element = reference.element
                    val parent = element.parent
                    
                    // Check if this reference is used in an event() call
                    when {
                        // Check for event(new EventClass(...))
                        parent is NewExpression -> {
                            val grandParent = parent.parent
                            if (grandParent is ParameterList) {
                                val functionCall = grandParent.parent
                                if (functionCall is FunctionReference && functionCall.name == "event") {
                                    publishers.add(functionCall)
                                }
                            }
                        }
                        
                        // Check for event(EventClass::class) or similar patterns
                        parent is ClassConstantReference -> {
                            val grandParent = parent.parent
                            if (grandParent is ParameterList) {
                                val functionCall = grandParent.parent
                                if (functionCall is FunctionReference && functionCall.name == "event") {
                                    publishers.add(functionCall)
                                }
                            }
                        }
                        
                        // Check for Event::dispatch(new EventClass(...))
                        parent is NewExpression && parent.parent is ParameterList -> {
                            val methodCall = parent.parent?.parent
                            if (methodCall is MethodReference && 
                                methodCall.name == "dispatch" && 
                                methodCall.classReference?.name == "Event") {
                                publishers.add(methodCall)
                            }
                        }
                    }
                }
            }
            
            // Fallback to the original implementation for string-based event names
            val scope = GlobalSearchScope.projectScope(project)
            val phpFiles = FileTypeIndex.getFiles(
                com.intellij.openapi.fileTypes.FileTypeManager.getInstance().getFileTypeByExtension("php"),
                scope
            )

            for (virtualFile in phpFiles) {
                val psiFile = PsiManager.getInstance(project).findFile(virtualFile) ?: continue
                if (!psiFile.isValid) continue

                // Find all function calls in the file
                val functionCalls = PsiTreeUtil.findChildrenOfType(psiFile, FunctionReference::class.java)
                
                for (functionCall in functionCalls) {
                    if (!functionCall.isValid) continue
                    
                    // Check if this is an event() call
                    if (functionCall.name != "event") continue
                    
                    // Check if it's dispatching our event (string-based)
                    val dispatchedEvent = extractEventClassFromCall(functionCall)
                    if (dispatchedEvent == eventClass || 
                        dispatchedEvent?.endsWith("\\" + eventClassName) == true ||
                        dispatchedEvent?.endsWith(eventClassName) == true) {
                        // Avoid duplicates
                        if (!publishers.contains(functionCall)) {
                            publishers.add(functionCall)
                        }
                    }
                }
                
                // Also check for Event::dispatch() pattern
                val methodCalls = PsiTreeUtil.findChildrenOfType(psiFile, MethodReference::class.java)
                
                for (methodCall in methodCalls) {
                    if (!methodCall.isValid) continue
                    
                    // Check if this is EventClass::dispatch()
                    if (methodCall.name == "dispatch") {
                        val classRef = methodCall.classReference?.name
                        if (classRef == eventClass || 
                            classRef?.endsWith("\\" + eventClassName) == true ||
                            classRef?.endsWith(eventClassName) == true) {
                            publishers.add(methodCall)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return publishers
    }
    
    private fun findListenersFromEventServiceProvider(project: Project, eventClass: String): List<PsiElement> {
        val listeners = mutableListOf<PsiElement>()
        
        try {
            // Find EventServiceProvider file
            val providerPath = project.basePath + "/app/Providers/EventServiceProvider.php"
            val providerFile = com.intellij.openapi.vfs.LocalFileSystem.getInstance().findFileByPath(providerPath)
            
            if (providerFile != null && providerFile.exists()) {
                val psiFile = PsiManager.getInstance(project).findFile(providerFile) ?: return listeners
                
                // Find the $listen array
                val phpClasses = PsiTreeUtil.findChildrenOfType(psiFile, PhpClass::class.java)
                
                for (phpClass in phpClasses) {
                    if (!phpClass.name?.contains("EventServiceProvider")!! ?: false) continue
                    
                    // Find the $listen property
                    val fields = phpClass.fields
                    for (field in fields) {
                        if (field.name == "listen" || field.name == "\$listen") {
                            // Parse the array to find listeners for our event
                            val listenersForEvent = parseListenArray(field, eventClass, project)
                            listeners.addAll(listenersForEvent)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return listeners
    }
    
    private fun parseListenArray(field: Field, eventClass: String, project: Project): List<PsiElement> {
        val listeners = mutableListOf<PsiElement>()
        
        // This is a simplified version - in production you'd want more robust parsing
        // Look for listener class names associated with the event
        
        return listeners
    }
    
    private fun isListenerClass(phpClass: PhpClass): Boolean {
        val className = phpClass.name ?: return false
        val namespace = phpClass.namespaceName
        
        // Check if class is in Listeners namespace or has Listener in name
        return namespace.contains("\\Listeners\\") || 
               namespace.endsWith("\\Listeners") ||
               className.contains("Listener")
    }
}