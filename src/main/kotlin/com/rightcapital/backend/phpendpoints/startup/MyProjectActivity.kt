package com.rightcapital.backend.phpendpoints.startup

import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity
import com.intellij.openapi.vfs.VirtualFile

class MyProjectActivity : ProjectActivity {

    private val logger = thisLogger()

    override suspend fun execute(project: Project) {
        logger.info("PHP Endpoints plugin initialized for project: ${project.name}")

        // Pre-warm the scanner service if this is a PHP project
        try {
            val baseDir = project.baseDir
            if (baseDir != null && hasPhpFiles(baseDir)) {
                logger.info("PHP project detected, initializing endpoint scanner")
            }
        } catch (e: Exception) {
            logger.warn("Error during PHP Endpoints plugin initialization: ${e.message}")
        }
    }

    private fun hasPhpFiles(dir: VirtualFile): Boolean {
        return try {
            dir.children.any { child ->
                when {
                    child.isDirectory && !child.name.startsWith(".") -> hasPhpFiles(child)
                    child.extension?.lowercase() == "php" -> true
                    else -> false
                }
            }
        } catch (e: Exception) {
            false
        }
    }
}