package com.rightcapital.backend.phpendpoints.models

/**
 * Represents an API endpoint found in PHP code
 */
data class ApiEndpoint(
    val url: String,
    val httpMethod: String,
    val controllerClass: String?,
    val methodName: String?,
    val filePath: String,
    val lineNumber: Int,
    val framework: PhpFramework,
    val description: String? = null,
    val parameters: List<EndpointParameter> = emptyList(),
    val fullControllerPath: String? = null // Full namespace path like "App\\Http\\Controllers\\UserController"
) {
    fun getDisplayName(): String {
        return "$httpMethod $url"
    }
    
    fun getFullMethodName(): String {
        return if (controllerClass != null && methodName != null) {
            "$controllerClass::$methodName"
        } else {
            methodName ?: "Unknown"
        }
    }
}

/**
 * Represents a parameter of an API endpoint
 */
data class EndpointParameter(
    val name: String,
    val type: String?,
    val required: Boolean = false,
    val description: String? = null
)

/**
 * Supported PHP frameworks
 */
enum class PhpFramework(val displayName: String) {
    LARAVEL("Laravel"),
    SYMFONY("Symfony"),
    CODEIGNITER("CodeIgniter"),
    SLIM("Slim"),
    NATIVE("Native PHP"),
    UNKNOWN("Unknown")
}

/**
 * HTTP methods supported
 */
enum class HttpMethod(val value: String) {
    GET("GET"),
    POST("POST"),
    PUT("PUT"),
    DELETE("DELETE"),
    PATCH("PATCH"),
    HEAD("HEAD"),
    OPTIONS("OPTIONS");
    
    companion object {
        fun fromString(method: String): HttpMethod? {
            return values().find { it.value.equals(method, ignoreCase = true) }
        }
    }
}
