<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <id>com.rightcapital.backend.phpendpoints</id>
    <name>PHP Endpoints</name>
    <vendor>RightCapital backend team</vendor>

    <description><![CDATA[
    A powerful PhpStorm plugin that scans and displays all API endpoints in your PHP projects.

    Features:
    - Automatically detects API endpoints in Laravel, Symfony, and native PHP projects
    - Displays endpoints in an organized table with HTTP methods, URLs, and controller information
    - Supports multiple PHP frameworks with intelligent parsing
    - Click to navigate directly to endpoint definitions in your code
    - Real-time scanning and caching for better performance
    ]]></description>

    <depends>com.intellij.modules.platform</depends>
    <depends>com.jetbrains.php</depends>

    <resource-bundle>messages.MyBundle</resource-bundle>

    <extensions defaultExtensionNs="com.intellij">
        <toolWindow
            factoryClass="com.rightcapital.backend.phpendpoints.toolWindow.MyToolWindowFactory"
            id="PHP Endpoints"
            displayName="PHP Endpoints"
            icon="/icons/php-endpoints.svg"
            anchor="bottom"
            secondary="true"/>
        <postStartupActivity implementation="com.rightcapital.backend.phpendpoints.startup.MyProjectActivity" />
        <applicationConfigurable 
            parentId="tools" 
            instance="com.rightcapital.backend.phpendpoints.settings.PhpEndpointsConfigurable"
            id="com.rightcapital.backend.phpendpoints.settings"
            displayName="PHP Endpoints"/>
        <codeInsight.lineMarkerProvider 
            language="PHP"
            implementationClass="com.rightcapital.backend.phpendpoints.lineMarkers.EloquentEventLineMarkerProvider"/>
        <codeInsight.lineMarkerProvider 
            language="PHP"
            implementationClass="com.rightcapital.backend.phpendpoints.lineMarkers.LaravelEventLineMarkerProvider"/>
        <codeInsight.lineMarkerProvider 
            language="PHP"
            implementationClass="com.rightcapital.backend.phpendpoints.lineMarkers.SnapshotLineMarkerProvider"/>
    </extensions>
</idea-plugin>
