projectService=Project service: {0}
randomLabel=The random number is: {0}
shuffle=Shuffle

# PHP Endpoints Plugin Messages
endpoints.toolWindow.title=PHP Endpoints
endpoints.scan.starting=Starting endpoint scan...
endpoints.scan.completed=Found {0} endpoints
endpoints.scan.error=Error scanning endpoints: {0}
endpoints.framework.detected=Framework detected: {0}
endpoints.refresh.button=Refresh
endpoints.status.ready=Ready
endpoints.table.method=Method
endpoints.table.url=URL
endpoints.table.controller=Controller
endpoints.table.methodName=Method Name
endpoints.table.framework=Framework
endpoints.table.file=File
endpoints.doubleClick.hint=Double-click to open file

# PHP Endpoints Settings Messages
settings.php.executable.path=Override PHP Executable Path
settings.php.executable.description=Leave empty to use PhpStorm's configured PHP interpreter (recommended)
settings.php.version=PHP Version
settings.phpstorm.config=PhpStorm Project Configuration
settings.phpstorm.config.description=Current PHP interpreter configured in PhpStorm project settings
settings.php.invalid=PHP executable is not valid or not found
settings.php.detection.failed=Could not detect PHP version
settings.php.priority.order=Priority: 1) PhpStorm project interpreter 2) Override path 3) System detection
